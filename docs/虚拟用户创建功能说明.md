# 用户创建功能说明

## 功能概述

为了满足密评任务需求，系统新增了批量创建用户的功能。该功能可以创建指定数量的用户，并对用户名和密码都进行加密处理，同时完成签名操作。

## 主要特性

1. **用户名加密**：使用SAC商用密码对用户名进行加密
2. **密码加密**：使用BCrypt + SAC双重加密
3. **自动签名**：自动对用户关键字段进行签名
4. **角色分配**：自动为用户分配默认角色（普通角色，ID=2）
5. **批量创建**：支持一次性创建多个用户

## 接口说明

### 批量创建用户

**接口地址：** `POST /sac/password/init/users`

**请求参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| userCount | int | 否 | 200 | 创建用户数量（1-1000） |
| userPrefix | String | 否 | "juneyao" | 用户名前缀 |
| defaultPassword | String | 否 | "123456" | 默认弱密码 |

**请求示例：**
```bash
curl -X POST "http://localhost:8080/sac/password/init/users" \
  -H "Content-Type: application/json" \
  -d '{
    "userCount": 200,
    "userPrefix": "juneyao",
    "defaultPassword": "123456"
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "批量创建用户完成",
  "data": {
    "totalCount": 200,
    "successCount": 200,
    "failCount": 0,
    "skippedCount": 0,
    "errorMessage": null
  }
}
```

## 实现细节

### 1. 用户名生成规则

- 原始用户名格式：`{userPrefix}{序号}`
- 序号为3位数字，如：001, 002, 003...
- 示例：`juneyao001`, `juneyao002`

### 2. 加密处理

#### 用户名加密
- 使用 `SysUserNameCryptoService` 进行SAC加密
- 先将用户名转换为HEX编码
- 再使用SM4算法进行加密

#### 密码加密
- 第一步：使用BCrypt进行加密
- 第二步：使用SAC商用密码进行二次加密

### 3. 签名处理

- 对用户关键字段（用户名、密码、手机号）进行签名
- 对用户角色关联进行签名
- 使用加密后的数据进行签名

### 4. 角色分配

- 自动为用户分配角色ID为2的"普通角色"
- 在app_id=2的应用下建立用户角色关联

## 相关文件

### 核心服务类

1. **SysVirtualUserCreateServiceImpl** - 测试用户创建服务
   - 位置：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysVirtualUserCreateServiceImpl.java`
   - 功能：批量创建测试用户的主要逻辑

2. **SysUserNameCryptoService** - 用户名加密服务
   - 位置：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/SysUserNameCryptoService.java`
   - 功能：用户名的SAC加密和解密

### 控制器

**SysPasswordCryptoInitController** - 密码加密初始化控制器
- 位置：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/controller/SysPasswordCryptoInitController.java`
- 新增接口：`/sac/password/init/test/users`

### 测试类

**SysVirtualUserCreateServiceTest** - 测试用户创建服务测试
- 位置：`ruoyi-modules/ruoyi-system/src/test/java/com/ruoyi/system/service/SysVirtualUserCreateServiceTest.java`

## 使用注意事项

1. **SAC服务依赖**：功能依赖SAC商用密码服务，确保服务可用
2. **数据库权限**：需要对sys_user、sys_user_role等表有写权限
3. **用户名唯一性**：系统会检查用户名唯一性，重复的会跳过
4. **批量限制**：单次最多创建1000个用户
5. **默认部门**：测试用户默认分配到部门ID为103的部门

## 错误处理

- 如果用户名已存在（原始或加密后），会跳过该用户
- 如果SAC加密失败，会使用原始数据
- 如果签名失败，会记录警告但不影响用户创建
- 详细的错误信息会记录在日志中

## 监控和日志

- 每处理50个用户会输出一次进度日志
- 成功、失败、跳过的用户都会有详细日志记录
- 可以通过返回的统计信息了解执行结果

## 使用示例

### 创建200个测试用户
```bash
curl -X POST "http://localhost:8080/sac/password/init/test/users" \
  -H "Content-Type: application/json" \
  -d '{
    "userCount": 200,
    "userPrefix": "juneyao",
    "defaultPassword": "123456"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "msg": "批量创建测试用户完成",
  "data": {
    "totalCount": 200,
    "successCount": 200,
    "failCount": 0,
    "skippedCount": 0,
    "errorMessage": null
  }
}
```

## 安全考虑

1. **权限控制**：建议为该接口配置适当的权限控制
2. **访问限制**：建议限制只有管理员可以调用
3. **审计日志**：所有操作都会记录在系统日志中
4. **数据加密**：用户名和密码都经过加密处理
5. **数据签名**：关键数据都经过数字签名保护
