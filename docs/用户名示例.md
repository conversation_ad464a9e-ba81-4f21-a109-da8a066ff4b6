# 测试用户命名示例

## 用户名生成规则

使用 `juneyao` 作为前缀，后面跟3位数字序号：

### 原始用户名格式
```
juneyao001
juneyao002
juneyao003
...
juneyao200
```

### 加密后用户名
原始用户名会经过SAC商用密码加密，加密后的格式类似：
```
A1B2C3D4E5F6789012345678901234567890ABCDEF1234567890ABCDEF123456
```

### 其他用户信息

#### 昵称
```
测试用户001
测试用户002
测试用户003
...
```

#### 邮箱
```
<EMAIL>
<EMAIL>
<EMAIL>
...
```

#### 手机号
```
13800000001
13800000002
13800000003
...
```

## 数据库存储

在数据库中，用户名字段存储的是加密后的值，但其他字段（昵称、邮箱等）保持明文，便于识别和管理。

## 安全特性

1. **用户名加密**: 使用SAC商用密码算法加密
2. **密码双重加密**: BCrypt + SAC双重加密
3. **数据签名**: 对关键字段进行数字签名
4. **唯一性检查**: 确保原始和加密后的用户名都不重复

## 使用场景

这些测试用户主要用于：
- 密评任务测试
- 系统压力测试
- 功能验证测试
- 安全性测试

## 注意事项

1. 测试用户默认密码为 `123456`（经过双重加密）
2. 所有测试用户分配到普通角色（ID=2）
3. 默认部门为ID=103的部门
4. 创建时会自动完成签名处理
