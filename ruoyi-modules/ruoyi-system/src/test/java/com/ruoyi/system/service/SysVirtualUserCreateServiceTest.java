package com.ruoyi.system.service;

import com.ruoyi.system.service.impl.SysVirtualUserCreateServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 测试用户创建服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SysVirtualUserCreateServiceTest {

    @Autowired
    private SysVirtualUserCreateServiceImpl sysVirtualUserCreateService;

    /**
     * 测试批量创建测试用户
     */
    @Test
    public void testCreateTestUsers() {
        // 创建少量测试用户
        SysVirtualUserCreateServiceImpl.VirtualUserCreateResult result =
            sysVirtualUserCreateService.createTestUsers(5, "juneyao", "123456");

        assertNotNull("创建结果不应为空", result);
        assertEquals("总数应该为5", 5, result.totalCount);
        assertTrue("成功数应该大于等于0", result.successCount >= 0);
        assertTrue("失败数应该大于等于0", result.failCount >= 0);
        assertTrue("跳过数应该大于等于0", result.skippedCount >= 0);
        assertEquals("成功+失败+跳过应该等于总数", result.totalCount, 
            result.successCount + result.failCount + result.skippedCount);

        System.out.println("测试用户创建结果：");
        System.out.println("总数: " + result.totalCount);
        System.out.println("成功: " + result.successCount);
        System.out.println("失败: " + result.failCount);
        System.out.println("跳过: " + result.skippedCount);
        if (result.errorMessage != null) {
            System.out.println("错误信息: " + result.errorMessage);
        }
    }

    @Autowired
    private SysUserNameCryptoService sysUserNameCryptoService;

    /**
     * 测试用户名加密服务
     */
    @Test
    public void testUserNameCrypto() {
        String originalUserName = "juneyao001";

        // 注意：这个测试可能会失败，因为需要SAC服务支持
        // 在实际环境中运行时才能正常工作
        try {
            String encryptedUserName = sysUserNameCryptoService.encryptUserName(originalUserName);
            assertNotNull("加密后用户名不应为空", encryptedUserName);

            String decryptedUserName = sysUserNameCryptoService.decryptUserName(encryptedUserName);
            assertEquals("解密后应该等于原始用户名", originalUserName, decryptedUserName);

            System.out.println("原始用户名: " + originalUserName);
            System.out.println("加密后用户名: " + encryptedUserName);
            System.out.println("解密后用户名: " + decryptedUserName);
        } catch (Exception e) {
            System.out.println("用户名加密测试失败（可能是SAC服务不可用）: " + e.getMessage());
        }
    }
}
