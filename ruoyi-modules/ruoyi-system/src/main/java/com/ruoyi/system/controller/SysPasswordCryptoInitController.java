package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.service.impl.SysPasswordCryptoInitServiceImpl;
import com.ruoyi.system.service.impl.SysVirtualUserCreateServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 密码加密初始化控制器
 * 提供用户密码批量SAC加密初始化功能
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sac/password/init")
public class SysPasswordCryptoInitController extends BaseController {

    @Autowired
    private SysPasswordCryptoInitServiceImpl sysPasswordCryptoInitService;

    @Autowired
    private SysVirtualUserCreateServiceImpl sysVirtualUserCreateService;

    /**
     * 初始化所有用户密码加密
     */
    @Log(title = "密码加密初始化", businessType = BusinessType.OTHER)
    //@RequiresPermissions("system:password:crypto:init")
    @PostMapping("/all")
    public AjaxResult initAllPasswordCrypto() {
        try {
            SysPasswordCryptoInitServiceImpl.PasswordCryptoInitResult result = 
                sysPasswordCryptoInitService.initAllPasswordCrypto();
            
            if (result.errorMessage != null) {
                return AjaxResult.error("密码加密初始化失败: " + result.errorMessage);
            }
            
            return AjaxResult.success("密码加密初始化完成", result);
        } catch (Exception e) {
            return AjaxResult.error("密码加密初始化失败: " + e.getMessage());
        }
    }

    /**
     * 分批初始化用户密码加密
     *
     * @param batchSize 每批处理的数量，默认1000
     * @param maxBatches 最大批次数，默认0（处理全部）
     */
    @Log(title = "分批密码加密初始化", businessType = BusinessType.OTHER)
    //@RequiresPermissions("system:password:crypto:init")
    @PostMapping("/batch")
    public AjaxResult initPasswordCryptoBatch(
            @RequestParam(defaultValue = "1000") int batchSize,
            @RequestParam(defaultValue = "0") int maxBatches) {
        try {
            // 参数验证
            if (batchSize <= 0) {
                return AjaxResult.error("批次大小必须大于0");
            }
            if (maxBatches < 0) {
                return AjaxResult.error("最大批次数不能小于0");
            }

            SysPasswordCryptoInitServiceImpl.PasswordCryptoInitResult result =
                sysPasswordCryptoInitService.initPasswordCryptoBatch(batchSize, maxBatches);

            if (result.errorMessage != null) {
                return AjaxResult.error("分批密码加密初始化失败: " + result.errorMessage);
            }

            return AjaxResult.success("分批密码加密初始化完成", result);
        } catch (Exception e) {
            return AjaxResult.error("分批密码加密初始化失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建虚拟用户（用于密评任务）
     * 创建指定数量的虚拟用户，用户名和密码都进行加密处理
     *
     * @param userCount 创建用户数量，默认200
     * @param userPrefix 用户名前缀，默认"virtual_user_"
     * @param defaultPassword 默认弱密码，默认"123456"
     */
    @Log(title = "批量创建虚拟用户", businessType = BusinessType.OTHER)
    //@RequiresPermissions("system:virtual:user:create")
    @PostMapping("/virtual/users")
    public AjaxResult createVirtualUsers(
            @RequestParam(defaultValue = "200") int userCount,
            @RequestParam(defaultValue = "virtual_user_") String userPrefix,
            @RequestParam(defaultValue = "123456") String defaultPassword) {
        try {
            // 参数验证
            if (userCount <= 0 || userCount > 1000) {
                return AjaxResult.error("用户数量必须在1-1000之间");
            }
            if (userPrefix == null || userPrefix.trim().isEmpty()) {
                return AjaxResult.error("用户名前缀不能为空");
            }
            if (defaultPassword == null || defaultPassword.trim().isEmpty()) {
                return AjaxResult.error("默认密码不能为空");
            }

            SysVirtualUserCreateServiceImpl.VirtualUserCreateResult result =
                sysVirtualUserCreateService.createVirtualUsers(userCount, userPrefix.trim(), defaultPassword.trim());

            if (result.errorMessage != null) {
                return AjaxResult.error("批量创建虚拟用户失败: " + result.errorMessage);
            }

            return AjaxResult.success("批量创建虚拟用户完成", result);
        } catch (Exception e) {
            return AjaxResult.error("批量创建虚拟用户失败: " + e.getMessage());
        }
    }

}
